# SbmagicTop - Webhook Auto-Update Setup Guide

## Tính năng mới
Plugin SbmagicTop đã được cập nhật với tính năng webhook auto-update:
- Tự động cập nhật leaderboard mỗi 1 tiếng (có thể tùy chỉnh)
- Cậ<PERSON> nhật khi server khởi động
- Cập nhật khi reload plugin
- Tự động tạo message mới hoặc edit message cũ
- Lưu trữ message IDs để có thể edit sau này

## Cấu hình

### 1. C<PERSON>u hình Discord Bot
Trong file `config.yml`:

```yaml
discord:
  token: "YOUR_BOT_TOKEN_HERE"
  guild-id: "YOUR_GUILD_ID_HERE"
  
  # Webhook settings for auto-update
  webhook:
    # Webhook URL for sending leaderboard updates (không bắt buộc nếu dùng channel-id)
    url: "YOUR_WEBHOOK_URL_HERE"
    # Channel ID where webhook messages will be sent
    channel-id: "YOUR_CHANNEL_ID_HERE"
    # Enable/disable webhook functionality
    enabled: true
    # Username for webhook messages
    username: "SkyBlock Leaderboard"
    # Avatar URL for webhook messages
    avatar-url: ""

settings:
  # Auto-update settings
  auto-update:
    # Enable auto-update feature
    enabled: true
    # Update interval in minutes (default: 60 minutes = 1 hour)
    interval: 60
    # Update on server startup
    on-startup: true
    # Update on plugin reload
    on-reload: true
```

### 2. Cấu hình từng loại Top
Trong các file `top/*.yml` (ví dụ: `balance.yml`, `donate.yml`):

```yaml
name: 💰 Top Balance
description: Bảng Xếp Hạng Balance
color: 0xFFD700
thumbnail: ''

# Webhook settings for this specific top
webhook:
  enabled: true
  url: ''  # Leave empty to use global webhook URL
  update-interval: 60  # Minutes

content:
  - "🥇 %ajlb_lb_vault_eco_balance_1_alltime_name% - %ajlb_lb_vault_eco_balance_1_alltime_value%"
  # ... more content
```

## Lệnh quản lý

### Lệnh cơ bản
- `/sbmagictop reload` - Reload toàn bộ config và restart auto-update
- `/sbmagictop status` - Xem trạng thái plugin và webhook
- `/sbmagictop update` - Trigger cập nhật webhook ngay lập tức

### Lệnh webhook
- `/sbmagictop webhook status` - Xem trạng thái webhook chi tiết
- `/sbmagictop webhook update` - Cập nhật webhook thủ công
- `/sbmagictop webhook clear` - Xóa tất cả message IDs (sẽ tạo message mới)

## Cách hoạt động

1. **Khởi động**: Plugin sẽ tự động tạo message leaderboard trong channel được chỉ định
2. **Cập nhật định kỳ**: Mỗi 1 tiếng (hoặc theo cấu hình), plugin sẽ edit message cũ với dữ liệu mới
3. **Lưu trữ**: Message IDs được lưu trong file `webhook-data.yml` để có thể edit sau này
4. **Fallback**: Nếu không thể edit message cũ (bị xóa, lỗi), plugin sẽ tự động tạo message mới

## Troubleshooting

### Webhook không hoạt động
1. Kiểm tra `discord.webhook.enabled: true`
2. Kiểm tra `discord.webhook.channel-id` có đúng không
3. Kiểm tra bot có quyền gửi message trong channel không
4. Xem log để kiểm tra lỗi

### Message không được edit
1. Kiểm tra message có bị xóa không
2. Sử dụng `/sbmagictop webhook clear` để reset message IDs
3. Plugin sẽ tự động tạo message mới nếu không thể edit

### Auto-update không chạy
1. Kiểm tra `settings.auto-update.enabled: true`
2. Kiểm tra `discord.webhook.enabled: true`
3. Kiểm tra interval có >= 5 phút không
4. Sử dụng `/sbmagictop status` để xem trạng thái

## File cấu hình được tạo

- `webhook-data.yml`: Lưu trữ message IDs của các webhook
- Cấu trúc:
```yaml
message-ids:
  balance: "1234567890123456789"
  donate: "9876543210987654321"
```

## Lưu ý quan trọng

1. **Channel ID vs Webhook URL**: Plugin sử dụng Channel ID thay vì Webhook URL để dễ quản lý hơn
2. **Rate Limiting**: Plugin có delay 1 giây giữa các update để tránh rate limit
3. **Permissions**: Bot cần quyền `Send Messages`, `Embed Links`, `Manage Messages` trong channel
4. **Backup**: Message IDs được lưu tự động, không cần backup thủ công

## Ví dụ cấu hình hoàn chỉnh

```yaml
discord:
  token: "MTA5MTYyNzA2OTUxNzA4Njc4MQ.GeQ5v8.sMosiTrRUtid4ot79lEaWG4JcY8iznrRrb8X2I"
  guild-id: "1395472849107423452"
  
  webhook:
    url: ""  # Không cần thiết nếu dùng channel-id
    channel-id: "1395472849107423455"  # ID của channel muốn gửi leaderboard
    enabled: true
    username: "SkyBlock Leaderboard"
    avatar-url: ""

settings:
  auto-update:
    enabled: true
    interval: 60  # 1 tiếng
    on-startup: true
    on-reload: true
```
