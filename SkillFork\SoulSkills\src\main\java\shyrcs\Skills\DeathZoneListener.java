package shyrcs.Skills;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import shyrcs.Ability.CooldownManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener xử lý Death Zone skill activation thông qua NBT tags
 * Format NBT: death_zone <click_type> <cooldown>
 * Ví dụ: death_zone left_click 30
 */
public class DeathZoneListener implements Listener {

    private final Plugin plugin;
    private final DeathZoneEffect deathZoneEffect;
    private final Map<UUID, Long> cooldowns = new HashMap<>();

    public DeathZoneListener(Plugin plugin) {
        this.plugin = plugin;
        this.deathZoneEffect = new DeathZoneEffect(plugin);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        if (item == null || !item.hasItemMeta()) {
            return;
        }

        // Đọc NBT tags để tìm death_zone skill
        NBTItem nbtItem = NBTItem.get(item);
        String deathZoneConfig = findDeathZoneConfig(nbtItem);

        if (deathZoneConfig == null) {
            return; // Không có death_zone skill
        }

        // Parse config: death_zone <click_type> <cooldown>
        String[] parts = deathZoneConfig.split(" ");
        if (parts.length != 3 || !parts[0].equals("death_zone")) {
            player.sendMessage("§c[Death Zone] §7Cấu hình NBT không hợp lệ! Format: death_zone <click_type> <cooldown>");
            return;
        }

        String requiredClickType = parts[1].toLowerCase();
        int cooldownSeconds;

        try {
            cooldownSeconds = Integer.parseInt(parts[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§c[Death Zone] §7Cooldown không hợp lệ!");
            return;
        }

        // Kiểm tra click type
        String actualClickType = getClickType(event.getAction(), player.isSneaking());
        if (!actualClickType.equals(requiredClickType)) {
            return; // Không phải click type đúng
        }

        // Cancel event để tránh các hành động khác
        event.setCancelled(true);

        // Kiểm tra cooldown
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        if (cooldowns.containsKey(playerId)) {
            long lastUse = cooldowns.get(playerId);
            long timePassed = (currentTime - lastUse) / 1000;

            if (timePassed < cooldownSeconds) {
                long remaining = cooldownSeconds - timePassed;

                // Sử dụng CooldownManager để hiển thị cooldown trên ActionBar
                CooldownManager cooldownManager = CooldownManager.getInstance();
                if (cooldownManager != null) {
                    cooldownManager.setCooldown(player, "Death Zone", (int) remaining);
                }

                player.sendMessage("§c[Death Zone] §7Còn §e" + remaining + "s §7cooldown!");
                return;
            }
        }

        // Kiểm tra nếu player đã có dome active
        if (deathZoneEffect.hasActiveDome(playerId)) {
            player.sendMessage("§c[Death Zone] §7Bạn đã có Death Zone đang hoạt động!");
            return;
        }

        // Kích hoạt Death Zone
        deathZoneEffect.activateDeathZone(player);

        // Set cooldown
        cooldowns.put(playerId, currentTime);
        CooldownManager cooldownManager = CooldownManager.getInstance();
        if (cooldownManager != null) {
            cooldownManager.setCooldown(player, "Death Zone", cooldownSeconds);
        }

        // Thông báo thành công
        player.sendMessage("§5[Death Zone] §7Đã kích hoạt Death Zone! Cooldown: §e" + cooldownSeconds + "s");
    }

    /**
     * Tìm death_zone config trong NBT tags của item
     * Tìm kiếm trong tất cả NBT tags có chứa "death_zone"
     */
    private String findDeathZoneConfig(NBTItem nbtItem) {
        // Kiểm tra các NBT tag phổ biến có thể chứa death_zone
        String[] possibleTags = {
            "MMOITEMS_DEATH_ZONE",
            "death_zone",
            "DEATH_ZONE",
            "skill_death_zone",
            "SKILL_DEATH_ZONE"
        };

        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }

        // Tìm kiếm trong tất cả NBT tags (fallback)
        for (String key : nbtItem.getTags()) {
            if (key.toLowerCase().contains("death") || key.toLowerCase().contains("zone")) {
                String value = nbtItem.getString(key);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }

        return null;
    }
    
    /**
     * Xác định loại click từ event
     */
    private String getClickType(Action action, boolean isSneaking) {
        String baseType;
        
        switch (action) {
            case LEFT_CLICK_AIR:
            case LEFT_CLICK_BLOCK:
                baseType = "left_click";
                break;
            case RIGHT_CLICK_AIR:
            case RIGHT_CLICK_BLOCK:
                baseType = "right_click";
                break;
            default:
                return "unknown";
        }
        
        if (isSneaking) {
            return "shift_" + baseType;
        } else {
            return baseType;
        }
    }
    
    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        deathZoneEffect.cleanup();
        cooldowns.clear();
    }
    
    /**
     * Lấy DeathZoneEffect instance
     */
    public DeathZoneEffect getDeathZoneEffect() {
        return deathZoneEffect;
    }
}
