package shyrcs.discordbot.top.managers;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.concrete.TextChannel;
import net.dv8tion.jda.api.entities.Webhook;
import net.dv8tion.jda.api.entities.WebhookClient;
import net.dv8tion.jda.api.requests.restaction.WebhookAction;
import org.bukkit.configuration.file.YamlConfiguration;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.models.TopConfig;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

public class WebhookManager {
    
    private final SbmagicTopPlugin plugin;
    private final Logger logger;
    private final File webhookDataFile;
    private YamlConfiguration webhookData;
    private final Map<String, String> messageIds; // topType -> messageId
    
    public WebhookManager(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.webhookDataFile = new File(plugin.getDataFolder(), "webhook-data.yml");
        this.messageIds = new HashMap<>();
        
        loadWebhookData();
    }
    
    private void loadWebhookData() {
        if (!webhookDataFile.exists()) {
            try {
                webhookDataFile.createNewFile();
                webhookData = new YamlConfiguration();
                saveWebhookData();
            } catch (IOException e) {
                logger.severe("Failed to create webhook data file: " + e.getMessage());
                return;
            }
        }
        
        webhookData = YamlConfiguration.loadConfiguration(webhookDataFile);
        
        // Load message IDs
        if (webhookData.contains("message-ids")) {
            for (String key : webhookData.getConfigurationSection("message-ids").getKeys(false)) {
                messageIds.put(key, webhookData.getString("message-ids." + key));
            }
        }
        
        logger.info("Loaded " + messageIds.size() + " webhook message IDs");
    }
    
    private void saveWebhookData() {
        try {
            // Save message IDs
            for (Map.Entry<String, String> entry : messageIds.entrySet()) {
                webhookData.set("message-ids." + entry.getKey(), entry.getValue());
            }
            
            webhookData.save(webhookDataFile);
        } catch (IOException e) {
            logger.severe("Failed to save webhook data: " + e.getMessage());
        }
    }
    
    public boolean isWebhookEnabled() {
        return plugin.getConfig().getBoolean("discord.webhook.enabled", false);
    }
    
    public String getWebhookUrl() {
        return plugin.getConfig().getString("discord.webhook.url", "");
    }
    
    public String getChannelId() {
        return plugin.getConfig().getString("discord.webhook.channel-id", "");
    }
    
    public String getWebhookUsername() {
        return plugin.getConfig().getString("discord.webhook.username", "SkyBlock Leaderboard");
    }
    
    public String getWebhookAvatarUrl() {
        return plugin.getConfig().getString("discord.webhook.avatar-url", "");
    }
    
    public CompletableFuture<Void> sendOrUpdateTopWebhook(String topType) {
        if (!isWebhookEnabled()) {
            return CompletableFuture.completedFuture(null);
        }
        
        String webhookUrl = getWebhookUrl();
        if (webhookUrl.isEmpty() || webhookUrl.equals("YOUR_WEBHOOK_URL_HERE")) {
            logger.warning("Webhook URL not configured properly");
            return CompletableFuture.completedFuture(null);
        }
        
        TopConfig topConfig = plugin.getConfigManager().getTopConfig(topType);
        if (topConfig == null) {
            logger.warning("Top config not found for type: " + topType);
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Create embed
                EmbedBuilder embed = createTopEmbed(topConfig);
                
                // Check if we have existing message ID
                String existingMessageId = messageIds.get(topType);
                
                if (existingMessageId != null) {
                    // Try to edit existing message
                    return editWebhookMessage(webhookUrl, existingMessageId, embed, topType);
                } else {
                    // Send new message
                    return sendNewWebhookMessage(webhookUrl, embed, topType);
                }
                
            } catch (Exception e) {
                logger.severe("Error in webhook operation for " + topType + ": " + e.getMessage());
                return false;
            }
        }).thenAccept(success -> {
            if (success) {
                logger.info("Successfully updated webhook for " + topType);
            } else {
                logger.warning("Failed to update webhook for " + topType);
            }
        });
    }
    
    private EmbedBuilder createTopEmbed(TopConfig topConfig) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("🏆 " + topConfig.getName());
        embed.setColor(new Color(topConfig.getColor()));
        embed.setTimestamp(Instant.now());
        
        // Set thumbnail if available
        if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
            embed.setThumbnail(topConfig.getThumbnail());
        }
        
        // Set footer
        String footerText = plugin.getConfig().getString("embed.footer", "SkyBlock Magic - Leaderboard");
        embed.setFooter(footerText + " • Cập nhật tự động mỗi giờ");
        
        // Parse and add content
        StringBuilder description = new StringBuilder();
        if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
            description.append(topConfig.getDescription()).append("\n\n");
        }
        
        for (String line : topConfig.getContent()) {
            String parsedLine = plugin.getPlaceholderManager().parsePlaceholders(line);
            String discordLine = plugin.getPlaceholderManager().convertMinecraftToDiscord(parsedLine);
            description.append(discordLine).append("\n");
        }
        
        embed.setDescription(description.toString());
        
        return embed;
    }
    
    private boolean sendNewWebhookMessage(String webhookUrl, EmbedBuilder embed, String topType) {
        try {
            // Use HTTP request to send webhook
            String channelId = getChannelId();
            if (channelId.isEmpty() || channelId.equals("YOUR_CHANNEL_ID_HERE")) {
                logger.warning("Channel ID not configured properly");
                return false;
            }

            TextChannel channel = plugin.getJDA().getTextChannelById(channelId);
            if (channel == null) {
                logger.warning("Channel not found: " + channelId);
                return false;
            }

            // Send message to channel and store ID
            channel.sendMessageEmbeds(embed.build()).queue(message -> {
                messageIds.put(topType, message.getId());
                saveWebhookData();
                logger.info("Sent new leaderboard message for " + topType + " with ID: " + message.getId());
            }, error -> {
                logger.severe("Failed to send message: " + error.getMessage());
            });

            return true;

        } catch (Exception e) {
            logger.severe("Failed to send new webhook message: " + e.getMessage());
            return false;
        }
    }
    
    private boolean editWebhookMessage(String webhookUrl, String messageId, EmbedBuilder embed, String topType) {
        try {
            String channelId = getChannelId();
            if (channelId.isEmpty() || channelId.equals("YOUR_CHANNEL_ID_HERE")) {
                logger.warning("Channel ID not configured properly");
                return false;
            }

            TextChannel channel = plugin.getJDA().getTextChannelById(channelId);
            if (channel == null) {
                logger.warning("Channel not found: " + channelId);
                return false;
            }

            // Edit existing message
            channel.editMessageEmbedsById(messageId, embed.build()).queue(
                success -> {
                    logger.info("Successfully edited leaderboard message for " + topType);
                },
                error -> {
                    logger.warning("Failed to edit message " + messageId + ", sending new message: " + error.getMessage());

                    // Remove invalid message ID and try sending new message
                    messageIds.remove(topType);
                    saveWebhookData();

                    sendNewWebhookMessage(webhookUrl, embed, topType);
                }
            );

            return true;

        } catch (Exception e) {
            logger.warning("Failed to edit webhook message " + messageId + ", sending new message: " + e.getMessage());

            // Remove invalid message ID and try sending new message
            messageIds.remove(topType);
            saveWebhookData();

            return sendNewWebhookMessage(webhookUrl, embed, topType);
        }
    }
    
    public void updateAllTops() {
        if (!isWebhookEnabled()) {
            return;
        }
        
        logger.info("Starting webhook update for all tops...");
        
        Map<String, TopConfig> allConfigs = plugin.getConfigManager().getAllTopConfigs();
        
        for (String topType : allConfigs.keySet()) {
            sendOrUpdateTopWebhook(topType);
            
            // Add small delay between updates to avoid rate limiting
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("Completed webhook update for " + allConfigs.size() + " tops");
    }
    
    public void clearMessageIds() {
        messageIds.clear();
        saveWebhookData();
        logger.info("Cleared all webhook message IDs");
    }
    
    public Map<String, String> getMessageIds() {
        return new HashMap<>(messageIds);
    }
}
