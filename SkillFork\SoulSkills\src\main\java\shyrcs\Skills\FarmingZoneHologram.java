package shyrcs.Skills;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.UUID;

/**
 * Class quản lý Hologram Head cho Farming Zone
 */
public class FarmingZoneHologram {

    private final Plugin plugin;
    private final Player owner;
    private final Location fixedLocation; // Vị trí cố định
    private ArmorStand hologramEntity;
    private BukkitTask rotationTask; // Task để xoay
    
    // Custom head texture (base64)
    private static final String HEAD_TEXTURE = "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNmRlZDg0MzkzYmVjMjUzY2NhNjZjMDQzMTY3MjZhODU4ZTU3NDcxMmI2ZmY5ZjI3MzY2OGRhOThlZmNjMjhmIn19fQ==";
    
    public FarmingZoneHologram(Plugin plugin, Player owner) {
        this.plugin = plugin;
        this.owner = owner;
        // Tạo vị trí cố định 6 blocks phía trên vị trí hiện tại của player
        this.fixedLocation = owner.getLocation().clone().add(0, 6, 0);
        createHologram();
        startRotationTask();
    }
    
    /**
     * Tạo hologram armor stand với custom head tại vị trí cố định
     */
    private void createHologram() {
        // Sử dụng vị trí cố định đã tính toán (6 blocks phía trên)
        Location hologramLocation = fixedLocation.clone();
        
        // Tạo armor stand
        hologramEntity = (ArmorStand) owner.getWorld().spawnEntity(hologramLocation, EntityType.ARMOR_STAND);
        
        // Cấu hình armor stand
        hologramEntity.setVisible(false); // Invisible body
        hologramEntity.setGravity(false); // No gravity
        hologramEntity.setCanPickupItems(false);
        hologramEntity.setRemoveWhenFarAway(false);
        hologramEntity.setInvulnerable(true);
        hologramEntity.setMarker(true); // Marker mode
        hologramEntity.setSmall(false);
        hologramEntity.setBasePlate(false);
        hologramEntity.setArms(false);
        
        // Tạo custom head với texture
        ItemStack customHead = createCustomHead();
        hologramEntity.setHelmet(customHead);
        
        // Set custom name (optional)
        hologramEntity.setCustomName("§a§lFarming Zone");
        hologramEntity.setCustomNameVisible(true);
    }
    
    /**
     * Tạo custom head với texture
     */
    private ItemStack createCustomHead() {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        
        if (meta != null) {
            try {
                // Sử dụng reflection để set texture
                setHeadTexture(meta, HEAD_TEXTURE);
                meta.setDisplayName("§a§lFarming Zone Head");
                head.setItemMeta(meta);
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to set custom head texture: " + e.getMessage());
                // Fallback to player head
                meta.setOwner(owner.getName());
                head.setItemMeta(meta);
            }
        }
        
        return head;
    }
    
    /**
     * Set head texture using reflection
     */
    private void setHeadTexture(SkullMeta meta, String texture) {
        try {
            // Create GameProfile
            Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
            Object gameProfile = gameProfileClass.getConstructor(UUID.class, String.class)
                .newInstance(UUID.randomUUID(), "FarmingZoneHead");
            
            // Create Property
            Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");
            Object property = propertyClass.getConstructor(String.class, String.class)
                .newInstance("textures", texture);
            
            // Add property to profile
            Object properties = gameProfileClass.getMethod("getProperties").invoke(gameProfile);
            properties.getClass().getMethod("put", Object.class, Object.class)
                .invoke(properties, "textures", property);
            
            // Set profile to skull meta
            java.lang.reflect.Field profileField = meta.getClass().getDeclaredField("profile");
            profileField.setAccessible(true);
            profileField.set(meta, gameProfile);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to set head texture", e);
        }
    }
    
    /**
     * Bắt đầu task để hologram xoay tròn tại vị trí cố định
     */
    private void startRotationTask() {
        rotationTask = new BukkitRunnable() {
            private float yaw = 0.0f;

            @Override
            public void run() {
                if (hologramEntity == null || hologramEntity.isDead() || !owner.isOnline()) {
                    this.cancel();
                    return;
                }

                // Tăng yaw để tạo hiệu ứng xoay tròn
                yaw += 5.0f; // Xoay 5 độ mỗi tick
                if (yaw >= 360.0f) {
                    yaw = 0.0f;
                }

                // Tạo location mới với yaw đã cập nhật
                Location rotatedLocation = fixedLocation.clone();
                rotatedLocation.setYaw(yaw);

                // Teleport hologram với rotation mới
                hologramEntity.teleport(rotatedLocation);
            }
        }.runTaskTimer(plugin, 0L, 2L); // Update every 2 ticks (0.1s) cho smooth rotation
    }
    
    /**
     * Lấy vị trí hiện tại của hologram (vị trí cố định)
     */
    public Location getLocation() {
        return fixedLocation.clone();
    }
    
    /**
     * Lấy player owner
     */
    public Player getOwner() {
        return owner;
    }
    
    /**
     * Kiểm tra hologram có còn valid không
     */
    public boolean isValid() {
        return hologramEntity != null && !hologramEntity.isDead() && owner.isOnline();
    }
    
    /**
     * Lấy ArmorStand entity
     */
    public ArmorStand getEntity() {
        return hologramEntity;
    }
    
    /**
     * Cleanup hologram
     */
    public void remove() {
        // Cancel rotation task
        if (rotationTask != null && !rotationTask.isCancelled()) {
            rotationTask.cancel();
        }

        // Remove armor stand
        if (hologramEntity != null && !hologramEntity.isDead()) {
            hologramEntity.remove();
        }

        hologramEntity = null;
        rotationTask = null;
    }
}
