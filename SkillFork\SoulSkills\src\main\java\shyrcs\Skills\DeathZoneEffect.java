package shyrcs.Skills;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.*;

/**
 * Class xử lý hiệu ứng Death Zone
 * Tạo dome particle tím, damage over time và barrier logic
 */
public class DeathZoneEffect {
    
    private static final double DOME_RADIUS = 15.0;
    private static final int DOME_DURATION = 5; // 5 seconds
    private static final double DAMAGE_PER_SECOND = 20.0;
    private static final int DOME_POINTS = 60; // Số điểm để tạo dome (giảm để tránh lag)
    
    private final Plugin plugin;
    private final Map<UUID, BukkitTask> activeDomes = new HashMap<>();
    private final Map<UUID, Set<UUID>> domePlayersInside = new HashMap<>();
    
    public DeathZoneEffect(Plugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Kích hoạt Death Zone tại vị trí player
     */
    public void activateDeathZone(Player caster) {
        UUID casterId = caster.getUniqueId();
        
        // Kiểm tra nếu player đã có dome active
        if (activeDomes.containsKey(casterId)) {
            caster.sendMessage("§c[Death Zone] §7Bạn đã có Death Zone đang hoạt động!");
            return;
        }
        
        Location center = caster.getLocation().clone();
        center.setY(center.getY() + DOME_RADIUS / 2); // Đặt tâm dome ở giữa
        
        caster.sendMessage("§5[Death Zone] §7Đã kích hoạt Death Zone!");
        
        // Tạo set để track players bên trong dome
        Set<UUID> playersInside = new HashSet<>();
        domePlayersInside.put(casterId, playersInside);
        
        // Bắt đầu effect task
        BukkitTask task = new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = DOME_DURATION * 20; // 5 seconds * 20 ticks
            
            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    // Kết thúc effect
                    endDeathZone(casterId);
                    this.cancel();
                    return;
                }
                
                // Tạo dome particles mỗi 4 ticks (0.2s) để giảm lag
                if (ticks % 4 == 0) {
                    createDomeParticles(center);
                }
                
                // Damage players mỗi 20 ticks (1s)
                if (ticks % 20 == 0) {
                    damagePlayersInDome(center, caster, playersInside);
                }
                
                // Kiểm tra và ngăn players thoát khỏi dome mỗi 5 ticks
                if (ticks % 5 == 0) {
                    preventEscape(center, playersInside);
                }
                
                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
        
        activeDomes.put(casterId, task);
    }
    
    /**
     * Tạo dome particles với hiệu ứng lồng giam
     */
    private void createDomeParticles(Location center) {
        // Tạo dome với pattern lồng giam (vertical lines)
        for (int i = 0; i < DOME_POINTS; i++) {
            double angle = 2 * Math.PI * i / DOME_POINTS;
            
            // Tạo các đường thẳng đứng từ đáy lên đỉnh dome
            for (int y = 0; y <= DOME_RADIUS; y += 2) {
                double currentRadius = Math.sqrt(DOME_RADIUS * DOME_RADIUS - (y - DOME_RADIUS/2) * (y - DOME_RADIUS/2));
                if (currentRadius < 0) continue;
                
                double x = currentRadius * Math.cos(angle);
                double z = currentRadius * Math.sin(angle);
                
                Location particleLocation = center.clone().add(x, y - DOME_RADIUS/2, z);
                
                // Sử dụng DRAGON_BREATH particle cho hiệu ứng tím
                center.getWorld().spawnParticle(Particle.DRAGON_BREATH, particleLocation, 1, 0, 0, 0, 0);
            }
            
            // Thêm horizontal rings để tạo hiệu ứng lồng
            if (i % 8 == 0) { // Chỉ tạo ring mỗi 8 điểm để giảm lag
                for (int ringY = 0; ringY <= DOME_RADIUS; ringY += 4) {
                    double ringRadius = Math.sqrt(DOME_RADIUS * DOME_RADIUS - (ringY - DOME_RADIUS/2) * (ringY - DOME_RADIUS/2));
                    if (ringRadius < 0) continue;
                    
                    for (int j = 0; j < 20; j++) {
                        double ringAngle = 2 * Math.PI * j / 20;
                        double ringX = ringRadius * Math.cos(ringAngle);
                        double ringZ = ringRadius * Math.sin(ringAngle);
                        
                        Location ringLocation = center.clone().add(ringX, ringY - DOME_RADIUS/2, ringZ);
                        center.getWorld().spawnParticle(Particle.WITCH, ringLocation, 1, 0, 0, 0, 0);
                    }
                }
            }
        }
    }
    
    /**
     * Damage players bên trong dome
     */
    private void damagePlayersInDome(Location center, Player caster, Set<UUID> playersInside) {
        for (Player player : center.getWorld().getPlayers()) {
            if (player.equals(caster)) continue; // Không damage caster
            
            double distance = player.getLocation().distance(center);
            if (distance <= DOME_RADIUS && isInsideDome(player.getLocation(), center)) {
                // Player ở trong dome
                playersInside.add(player.getUniqueId());
                
                // Damage bỏ qua giáp (set health trực tiếp)
                double currentHealth = player.getHealth();
                double newHealth = Math.max(0, currentHealth - DAMAGE_PER_SECOND);
                player.setHealth(newHealth);
                
                // Hiệu ứng damage
                player.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, 
                    player.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0);
                
                player.sendMessage("§c[Death Zone] §7Bạn nhận §c" + DAMAGE_PER_SECOND + " HP §7sát thương!");
            }
        }
    }
    
    /**
     * Ngăn players thoát khỏi dome
     */
    private void preventEscape(Location center, Set<UUID> playersInside) {
        for (UUID playerId : new HashSet<>(playersInside)) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                playersInside.remove(playerId);
                continue;
            }
            
            double distance = player.getLocation().distance(center);
            if (distance > DOME_RADIUS || !isInsideDome(player.getLocation(), center)) {
                // Player đang cố thoát khỏi dome, đẩy về trong
                Vector direction = center.toVector().subtract(player.getLocation().toVector()).normalize();
                Location newLocation = center.clone().add(direction.multiply(-DOME_RADIUS + 1));
                newLocation.setY(player.getLocation().getY()); // Giữ nguyên Y
                
                player.teleport(newLocation);
                player.sendMessage("§c[Death Zone] §7Bạn không thể thoát khỏi Death Zone!");
                
                // Hiệu ứng barrier (sử dụng cloud particle)
                player.getWorld().spawnParticle(Particle.CLOUD,
                    player.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0);
            }
        }
    }
    
    /**
     * Kiểm tra location có nằm trong dome không
     */
    private boolean isInsideDome(Location location, Location center) {
        double dx = location.getX() - center.getX();
        double dy = location.getY() - center.getY();
        double dz = location.getZ() - center.getZ();
        
        // Kiểm tra sphere equation: x² + y² + z² <= r²
        return (dx * dx + dy * dy + dz * dz) <= (DOME_RADIUS * DOME_RADIUS);
    }
    
    /**
     * Kết thúc Death Zone effect
     */
    private void endDeathZone(UUID casterId) {
        activeDomes.remove(casterId);
        domePlayersInside.remove(casterId);
        
        Player caster = Bukkit.getPlayer(casterId);
        if (caster != null && caster.isOnline()) {
            caster.sendMessage("§5[Death Zone] §7Death Zone đã kết thúc!");
        }
    }
    
    /**
     * Kiểm tra player có dome active không
     */
    public boolean hasActiveDome(UUID playerId) {
        return activeDomes.containsKey(playerId);
    }
    
    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        for (BukkitTask task : activeDomes.values()) {
            task.cancel();
        }
        activeDomes.clear();
        domePlayersInside.clear();
    }
}
