package shyrcs.Skills;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * Command để debug Death Zone NBT tags
 */
public class DeathZoneCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cChỉ player mới có thể sử dụng command này!");
            return true;
        }
        
        Player player = (Player) sender;
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || !item.hasItemMeta()) {
            player.sendMessage("§c[Death Zone Debug] §7Bạn cần cầm item có NBT tags!");
            return true;
        }
        
        player.sendMessage("§e[Death Zone Debug] §7Đang kiểm tra NBT tags...");
        
        NBTItem nbtItem = NBTItem.get(item);
        
        // Hiển thị tất cả NBT tags
        player.sendMessage("§a=== ALL NBT TAGS ===");
        boolean hasAnyTag = false;
        for (String key : nbtItem.getTags()) {
            hasAnyTag = true;
            String value = nbtItem.getString(key);
            player.sendMessage("§7- §e" + key + " §7= §f" + value);
        }
        
        if (!hasAnyTag) {
            player.sendMessage("§c- Không có NBT tags nào!");
        }
        
        // Kiểm tra các tag có thể chứa death_zone
        player.sendMessage("§a=== DEATH ZONE SEARCH ===");
        String[] possibleTags = {
            "MMOITEMS_DEATH_ZONE",
            "death_zone", 
            "DEATH_ZONE",
            "skill_death_zone",
            "SKILL_DEATH_ZONE"
        };
        
        boolean foundDeathZone = false;
        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                player.sendMessage("§a✓ Found: §e" + tag + " §7= §f" + value);
                if (value != null && value.startsWith("death_zone")) {
                    player.sendMessage("§a✓ Valid death_zone config!");
                    foundDeathZone = true;
                } else {
                    player.sendMessage("§c✗ Invalid format (should start with 'death_zone')");
                }
            }
        }
        
        // Fallback search
        player.sendMessage("§a=== FALLBACK SEARCH ===");
        for (String key : nbtItem.getTags()) {
            if (key.toLowerCase().contains("death") || key.toLowerCase().contains("zone")) {
                String value = nbtItem.getString(key);
                player.sendMessage("§e? Found potential: §e" + key + " §7= §f" + value);
                if (value != null && value.startsWith("death_zone")) {
                    player.sendMessage("§a✓ Valid death_zone config in fallback!");
                    foundDeathZone = true;
                }
            }
        }
        
        if (!foundDeathZone) {
            player.sendMessage("§c✗ No valid death_zone config found!");
            player.sendMessage("§7Expected format: death_zone <click_type> <cooldown>");
            player.sendMessage("§7Example: death_zone right_click 30");
        }
        
        return true;
    }
}
